//
//  IMYPopPlanAPI.m
//  IMYMe
//
//  Created by HBQ on 2025/3/21.
//

#import "IMYPopPlanAPI.h"
#import <IMYBaseKit/IMYBaseKit.h>
#import <IMYBaseKit/IMYPublic.h>

/**
 * 用户行为事件类型
 */
typedef NS_ENUM(NSInteger, IMYUserBehaviorEventType) {
    IMYUserBehaviorEventTypeColdStart = 1,
    IMYUserBehaviorEventTypeHotStart = 2
};

// 会话超时阈值（秒）- 超过此时间从后台回到前台视为热启动
static const NSTimeInterval kUserBehaviorSessionTimeout = 30 * 60; // 30分钟

@interface IMYPopPlanAPI ()

@property (nonatomic, assign) NSTimeInterval lastEnterBackgroundTime; // 上次进入后台的时间
@property (nonatomic, assign) BOOL hasReportedColdStart; // 是否已上报冷启动事件

@end

@implementation IMYPopPlanAPI

IMY_KYLIN_FUNC_PREMAIN {
    // 提前初始化以接收冷启动通知
    [IMYPopPlanAPI sharedInstance];
}

+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYPopPlanAPI *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
        [instance startUserBehaviorEventMonitoring];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - 接口

/// 获取投放计划
- (void)getPlanWithCode:(NSString *)code
              onSuccess:(void (^)(NSDictionary *dict))onSuccess
                onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:code forKey:@"code"];
    
    // ext
    NSMutableDictionary *ext = [[NSMutableDictionary alloc] init];
    NSNumber *stageType = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"chatAI/stageType" params:nil];
    [ext imy_setNonNilObject:stageType forKey:@"stage_type"];
    NSNumber *babyMonth = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/babyMonth" params:nil];
    [ext imy_setNonNilObject:babyMonth forKey:@"baby_month"];
    NSNumber *pregnancyWeek = [[IMYURIManager sharedInstance] runActionAndSyncResultWithPath:@"general/pregnancyWeek" params:nil];
    [ext imy_setNonNilObject:pregnancyWeek forKey:@"pregnancy_week"];
    [parameter imy_setNonNilObject:ext forKey:@"ext"];
    
    RACSignal *signal = [IMYServerRequest postPath:@"dialog/popplan" host:mgo_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

/// 全局频控策略获取
- (void)getGfcPolicyOnSuccess:(void (^)(NSDictionary *dict))onSuccess
                      onError:(void (^)(NSError *error))onError {
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    
    RACSignal *signal = [IMYServerRequest postPath:@"gfc/policy" host:mgo_seeyouyima_com params:parameter headers:nil];
    
    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];
    
    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess(x);
        }
    } error:^(NSError * _Nullable error) {
        if (onError) {
            onError(error);
        }
    }];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

/// 开始监听应用启动和状态变化
- (void)startUserBehaviorEventMonitoring {
    // 初始化后台时间
    self.lastEnterBackgroundTime = [[NSDate date] timeIntervalSince1970];
    self.hasReportedColdStart = NO;

    // 监听应用启动完成 - 冷启动
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleApplicationDidFinishLaunching:)
                                                 name:UIApplicationDidFinishLaunchingNotification
                                               object:nil];

    // 监听应用进入后台
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleApplicationDidEnterBackground:)
                                                 name:UIApplicationDidEnterBackgroundNotification
                                               object:nil];

    // 监听应用回到前台 - 可能的热启动
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(handleApplicationDidBecomeActive:)
                                                 name:UIApplicationDidBecomeActiveNotification
                                               object:nil];
}

/// 处理应用启动完成 - 冷启动事件
- (void)handleApplicationDidFinishLaunching:(NSNotification *)notification {
    if (!self.hasReportedColdStart) {
        self.hasReportedColdStart = YES;
        [self reportColdStartOnSuccess:nil onError:nil];
    }
}

/// 处理应用进入后台
- (void)handleApplicationDidEnterBackground:(NSNotification *)notification {
    self.lastEnterBackgroundTime = [[NSDate date] timeIntervalSince1970];
}

/// 处理应用回到前台 - 判断是否为热启动
- (void)handleApplicationDidBecomeActive:(NSNotification *)notification {
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSTimeInterval backgroundDuration = currentTime - self.lastEnterBackgroundTime;

    // 如果距离上次进入后台超过会话超时阈值，则视为热启动
    if (backgroundDuration > kUserBehaviorSessionTimeout) {
        [self reportHotStartOnSuccess:nil onError:nil];
    }
}

/// 冷启动事件上报
- (void)reportColdStartOnSuccess:(void (^)(void))onSuccess
                         onError:(void (^)(NSError *error))onError {
    [self reportUserBehaviorEvent:IMYUserBehaviorEventTypeColdStart appId:1 onSuccess:onSuccess onError:onError];
}

/// 热启动事件上报
- (void)reportHotStartOnSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError {
    [self reportUserBehaviorEvent:IMYUserBehaviorEventTypeHotStart appId:1 onSuccess:onSuccess onError:onError];
}

/// 用户行为事件上报
- (void)reportUserBehaviorEvent:(IMYUserBehaviorEventType)eventType
                          appId:(NSInteger)appId
                      onSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError {
    [self reportUserBehaviorEvent:eventType appId:appId retryCount:0 onSuccess:onSuccess onError:onError];
}

/// 用户行为事件上报（带重试机制）
- (void)reportUserBehaviorEvent:(IMYUserBehaviorEventType)eventType
                          appId:(NSInteger)appId
                     retryCount:(NSInteger)retryCount
                      onSuccess:(void (^)(void))onSuccess
                        onError:(void (^)(NSError *error))onError {

    if (eventType != IMYUserBehaviorEventTypeColdStart &&
        eventType != IMYUserBehaviorEventTypeHotStart) {
        if (onError) {
            NSError *error = [NSError errorWithDomain:@"IMYPopPlanAPI"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"无效的事件类型"}];
            onError(error);
        }
        return;
    }

    NSTimeInterval currentTimestamp = [[NSDate date] timeIntervalSince1970] * 1000;
    NSMutableDictionary *parameter = [NSMutableDictionary dictionary];
    [parameter imy_setNonNilObject:@(eventType) forKey:@"event"];
    [parameter imy_setNonNilObject:@((NSInteger)currentTimestamp) forKey:@"unix_timestamp"];
    [parameter imy_setNonNilObject:@(appId) forKey:@"app_id"];

    RACSignal *signal = [IMYServerRequest postPath:@"/event/track" host:mgo_seeyouyima_com params:parameter headers:nil];

    @weakify(self);
    RACSignal *resSignal = [signal flattenMap:^__kindof RACStream * _Nullable(id  _Nullable value) {
        @strongify(self);
        return [self.class responseFlattenMap:value];
    }];

    [[resSignal deliverOnMainThread] subscribeNext:^(id  _Nullable x) {
        if (onSuccess) {
            onSuccess();
        }
    } error:^(NSError * _Nullable error) {
        @strongify(self);
        [self handleUserBehaviorEventError:error
                                 eventType:eventType
                                     appId:appId
                                retryCount:retryCount
                                 onSuccess:onSuccess
                                   onError:onError];
    }];
}

/// 上报错误和重试逻辑
- (void)handleUserBehaviorEventError:(NSError *)error
                           eventType:(IMYUserBehaviorEventType)eventType
                               appId:(NSInteger)appId
                          retryCount:(NSInteger)retryCount
                           onSuccess:(void (^)(void))onSuccess
                             onError:(void (^)(NSError *error))onError {

    if (![IMYNetState networkEnable]) {
        if (onError) {
            onError(error);
        }
        return;
    }

    // 获取HTTP状态码
    NSHTTPURLResponse *httpResponse = error.af_httpResponse;
    NSInteger statusCode = httpResponse.statusCode;

    // 判断是否为5xx服务器错误且未重试过
    if (statusCode >= 500 && statusCode < 600 && retryCount == 0) {
        // 延迟1秒后重试
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self reportUserBehaviorEvent:eventType
                                    appId:appId
                               retryCount:retryCount + 1
                                onSuccess:onSuccess
                                  onError:onError];
        });
    } else {
        // 不满足重试条件，直接返回错误
        if (onError) {
            onError(error);
        }
    }
}

// MARK: - 转换

/// IMYHTTPResponse 转 NSDictionary 或 error
+ (RACSignal *)responseFlattenMap:(IMYHTTPResponse *)x {
    if ([x.responseObject isKindOfClass:[NSDictionary class]]) {
        return [RACSignal return:x.responseObject];
    } else {
        NSMutableDictionary *userInfo = [NSMutableDictionary dictionaryWithDictionary:x.userInfo];
        userInfo[AFNetworkingOperationFailingURLResponseDataErrorKey] = x.responseData;
        userInfo[AFNetworkingOperationFailingURLResponseErrorKey] = x.response;
        userInfo[NSLocalizedDescriptionKey] = @"网络缓慢，请稍后再试";
        return [RACSignal error:[NSError errorWithDomain:@"ChatAI" code:-9 userInfo:userInfo]];
    }
}

@end
