//
//  IMYPopPlanManager.m
//  IMYMe
//
//  Created by HBQ on 2025/3/21.
//

#import "IMYPopPlanManager.h"
#import "IMYPopPlanAPI.h"

@interface IMYPopPlanManager ()

@property (nonatomic, assign) BOOL hasRereshGfcPolicy;

@end

@implementation IMYPopPlanManager



+ (instancetype)sharedInstance {
    static dispatch_once_t onceToken;
    static IMYPopPlanManager *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[super allocWithZone:NULL] init];
    });
    return instance;
}

+ (id)allocWithZone:(struct _NSZone *)zone {
    return [self sharedInstance];
}

// MARK: - public

/// 获得 planModel: 返回该资源位（code）当前可以展示的 planModel。
/// 调用方还需获取具体素材：planModel 提供获取素材 id 的方法，调用方根据素材 id 去获取素材详情
- (IMYPopPlanModel *)getPlanModelWithCode:(NSString *)code
                               refreBlock:(void (^)(IMYPopPlanModel *refreshPlanModel))refreshBlock {
    // 频控
    IMYPopPlanModel *validPlanModel = [self firstValidPlanModelWithCode:code isCache:YES];
    
    // 每次调用都请求服务端数据，更新缓存
    @weakify(self);
    [self requestPlanWithCode:code cb:^(NSError *error) {
        @strongify(self);
        
        [self requestGfcPolicy:^(NSError *error) {
            @strongify(self);
            
            // 这个合法 planModel 通过 refreshBlock 返回给调用方，调用方要去请求素材详情然后缓存
            IMYPopPlanModel *refreshPlanModel = [self firstValidPlanModelWithCode:code isCache:NO];
            if (refreshBlock) {
                refreshBlock(refreshPlanModel);
            }
        }];
    }];
    
    return validPlanModel;
}

/// 保存曝光信息
- (void)saveShowInfoWithPlanId:(NSString *)planId
                      surveyId:(NSString *)surveyId {
    NSString *time = [[NSDate date] imy_getDateTimeString];
    [self cacheShowInfoWithTime:time planId:planId surveyId:surveyId];
    [self cacheLastExpInfo:time planId:planId surveyId:surveyId];
}

/// 保存关闭信息
- (void)saveCloseInfo:(NSString *)surveyId {
    [self cacheShowInfoWithClose:YES surveyId:surveyId];
}

// MARK: - 频控

/// 频控：从缓存的 planModel 中找到唯一合法的 planModel 抛出去
- (IMYPopPlanModel *)firstValidPlanModelWithCode:(NSString *)code isCache:(BOOL)isCache {
    NSArray *planList = [self getCachedPlanListWithCode:code];
    NSArray<IMYPopPlanModel *> *planModelList = [planList toModels:[IMYPopPlanModel class]];
    IMYPopPlanModel *validPlanModel = nil;
    
    NSString *start = [NSString stringWithFormat:@"开始检测 %@", [NSDate date].imy_getDateTimeString];
    [self ppmLog:start code:code isCache:isCache];
    for (IMYPopPlanModel *planModel in planModelList) {
        BOOL isPass = [self checkPlanModel:planModel code:code isCache:isCache];
        if (isPass) {
            validPlanModel = planModel;
            break;
        }
    }
    if (planModelList.count == 0) {
        [self ppmLog:@"计划为空" planId:nil surveyId:nil isCache:isCache];
    }
    
    return validPlanModel;
}

/// 检查当前 planModel 是否合法
- (BOOL)checkPlanModel:(IMYPopPlanModel *)planModel code:(NSString *)code isCache:(BOOL)isCache {
    NSString *planId = [NSString stringWithFormat:@"%ld", planModel.id];
    NSString *surveyId = [planModel surveyId];
    
    if ([planModel isExpired]) {
        NSString *detail = [NSString stringWithFormat:@"计划过期了 end_at %@", planModel.end_at];
        [self ppmLog:detail planId:planId surveyId:surveyId isCache:isCache];
        return NO;
    }
    
    if (imy_isEmptyString(surveyId)) {
        [self ppmLog:@"surveyId 不存在" planId:planId surveyId:surveyId isCache:isCache];
        return NO;
    }
    
    if (![planModel isValidForCurrentUserMode]) {
        [self ppmLog:@"当前身份不匹配该计划" planId:planId surveyId:surveyId isCache:isCache];
        
        NSString *detail = [NSString stringWithFormat:@"%@", planModel.userModes];
        [self ppmLog:detail planId:planId surveyId:surveyId isCache:isCache];
        return NO;
    }
    
    // 重置 show_rule
    BOOL hasRepeat = [self repeatShowRuleIfNeedWithSurveyId:surveyId planModel:planModel];
    if (hasRepeat) {
        [self ppmLog:@"重置 show_rule" planId:planId surveyId:surveyId isCache:isCache];
    }
    
    // [频控规则] showRule
    IMYPopPlanShowRule *show_rule = planModel.show_rule;
    NSInteger show_rule_interval_hour = show_rule.interval_hour;
    NSInteger show_rule_times = show_rule.times;
    
    // [频控规则] gfc
    NSInteger gfc_ex_day = -1;
    NSInteger gfc_show_interval = -1;
    NSDictionary *gfcDict = [self getCachedGfcPolicy];
    NSDictionary *gfc_material = gfcDict[@"gfc_material"];
    if (gfc_material) {
        NSDictionary *gfcData = gfc_material[@"3"];
        if (gfcData && [gfcData isKindOfClass:[NSDictionary class]]) {
            gfc_ex_day = [gfcData[@"ex_day"] integerValue];
            gfc_show_interval = [gfcData[@"show_interval"] integerValue];
        }
    }
    
    // 曝光信息
    NSDictionary *showInfo = [self getShowInfoWithSurveyId:surveyId];
    NSString *showInfo_createTime = showInfo[@"createTime"];
    NSString *showInfo_updateTime = showInfo[@"updateTime"];
    NSInteger showInfo_showTimes = [showInfo[@"showTimes"] integerValue];
    NSString *showInfo_updateTime4showRule = showInfo[@"updateTime4showRule"];
    NSInteger showInfo_showTimes4showRule = [showInfo[@"showTimes4showRule"] integerValue];
    NSDictionary *lastExpInfo = [self getLastExpInfo];
    NSString *lastShowTime = lastExpInfo[@"time"];
    if (imy_isEmptyString(lastShowTime)) {
        lastShowTime = @"";
    }
    NSString *lastShowSurveyId = lastExpInfo[@"surveyId"];
    if (imy_isEmptyString(lastShowSurveyId)) {
        lastShowSurveyId = @"";
    }
    BOOL close = [showInfo[@"close"] boolValue];
    
    // [check] close 不通过
    if (close) {
         [self ppmLog:@"close 不通过" planId:planId surveyId:surveyId isCache:isCache];
         return NO;
    }
    
    // [check] 负反馈不进频控
    BOOL isNegative = [planModel isNegative];
    if (isNegative) {
        [self ppmLog:@"负反馈不进频控-直接通过" planId:planId surveyId:surveyId isCache:isCache];
        return YES;
    }
    
    // [check] show_rule.interval_hour 不通过
    BOOL need_interval_hour = imy_isNotEmptyString(showInfo_updateTime4showRule) && show_rule_interval_hour > 0;
    if (need_interval_hour) {
        NSInteger interval_hour = [[showInfo_updateTime4showRule imy_getDateTime] hoursBeforeDate:[NSDate date]];
        if (interval_hour < show_rule_interval_hour) {
            [self ppmLog:@"show_rule.interval_hour 不通过" planId:planId surveyId:surveyId isCache:isCache];
            
            NSString *reason = [NSString stringWithFormat:@"%ld %ld", interval_hour, show_rule_interval_hour];
            [self ppmLog:reason planId:planId surveyId:surveyId isCache:isCache];
            return NO;
        }
    }
    
    // [check] show_rule.times 不通过
    BOOL need_times = showInfo_showTimes4showRule > 0 && show_rule_times > 0;
    if (need_times) {
        if (showInfo_showTimes4showRule >= show_rule_times) {
            [self ppmLog:@"show_rule.times 不通过" planId:planId surveyId:surveyId isCache:isCache];
            
            NSString *reason = [NSString stringWithFormat:@"%ld %ld", showInfo_showTimes4showRule, show_rule_times];
            [self ppmLog:reason planId:planId surveyId:surveyId isCache:isCache];
            return NO;
        }
    }
    
    // [check] gfc invalid 不通过
    if (gfc_ex_day < 0 || gfc_show_interval < 0) {
        [self ppmLog:@"gfc invalid 不通过" planId:planId surveyId:surveyId isCache:isCache];
                    
        NSString *reason = [NSString stringWithFormat:@"ex_day %ld show_interval %ld", gfc_ex_day, gfc_show_interval];
        [self ppmLog:reason planId:planId surveyId:surveyId isCache:isCache];
        return NO;
    }
    
    // [check] gfc.ex_day 不通过
    BOOL need_gfc_ex_day = imy_isNotEmptyString(showInfo_createTime);
    BOOL gfc_ex_day_pass = YES;
    if (need_gfc_ex_day) {
        NSDate *createDate = [[showInfo_createTime imy_getDateTime] imy_getDateZeroTime];
        NSDate *todayDate = [NSDate imy_today];
        NSInteger ex_day = [createDate daysBeforeDate:todayDate];
        if (ex_day >= gfc_ex_day) {
            [self ppmLog:@"gfc.ex_day 不通过(此时不拦截)" planId:planId surveyId:surveyId isCache:isCache];
                        
            NSString *reason = [NSString stringWithFormat:@"%ld %ld", ex_day, gfc_ex_day];
            [self ppmLog:reason planId:planId surveyId:surveyId isCache:isCache];
            gfc_ex_day_pass = NO;
        } else {
            [self ppmLog:@"gfc.ex_day 通过" planId:planId surveyId:surveyId isCache:isCache];
                        
            NSString *reason = [NSString stringWithFormat:@"%ld %ld", ex_day, gfc_ex_day];
            [self ppmLog:reason planId:planId surveyId:surveyId isCache:isCache];
        }
    }
    
    // [check] gfc.show_interval 不通过
    BOOL need_gfc_show_interval = imy_isNotEmptyString(lastShowTime);
    BOOL gfc_show_interval_pass = YES;
    if (need_gfc_show_interval) {
        NSInteger show_interval = [[lastShowTime imy_getDateTime] hoursBeforeDate:[NSDate date]];
        if (show_interval < gfc_show_interval) {
            [self ppmLog:@"gfc.show_interval 不通过(此时不拦截)" planId:planId surveyId:surveyId isCache:isCache];
                        
            NSString *reason = [NSString stringWithFormat:@"%ld %ld", show_interval, gfc_show_interval];
            [self ppmLog:reason planId:planId surveyId:surveyId isCache:isCache];
            gfc_show_interval_pass = NO;
        } else {
            [self ppmLog:@"gfc.show_interval 通过" planId:planId surveyId:surveyId isCache:isCache];
                        
            NSString *reason = [NSString stringWithFormat:@"%ld %ld", show_interval, gfc_show_interval];
            [self ppmLog:reason planId:planId surveyId:surveyId isCache:isCache];
        }
    }
    
    if (imy_isNotEmptyString(lastShowSurveyId) && [lastShowSurveyId isEqualToString:surveyId]) {
        // 新规则：1、如果上次曝光ID 和 当前ID一样，频控只考虑 ex_days
        if (!gfc_ex_day_pass) {
            [self ppmLog:@"新规则：1、如果上次曝光ID 和 当前ID一样，频控只考虑 ex_days（拦截）" planId:planId surveyId:surveyId isCache:isCache];
            return NO;
        }
    } else {
        // 新规则：2、如果上次曝光ID 和 当前ID不一样，频控先考虑 show_interval。
        if (!gfc_show_interval_pass) {
            [self ppmLog:@"新规则：2、如果上次曝光ID 和 当前ID不一样，频控先考虑 show_interval（拦截）" planId:planId surveyId:surveyId isCache:isCache];
            return NO;
        }
        
        // 新规则：2.1、如果 show_interval 通过，再考虑 ex_days
        if (!gfc_ex_day_pass) {
            [self ppmLog:@"新规则：2.1、如果 show_interval 通过，再考虑 ex_days（拦截）" planId:planId surveyId:surveyId isCache:isCache];
            return NO;
        }
    }
    
    [self ppmLog:@"通过" planId:planId surveyId:surveyId isCache:isCache];
    
    return YES;
}

// MARK: - data: server data

- (void)requestPlanWithCode:(NSString *)code 
                         cb:(void (^)(NSError *error))cb {
    @weakify(self);
    [[IMYPopPlanAPI sharedInstance] getPlanWithCode:code onSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        NSArray *list = dict[@"list"];
        if (list && [list isKindOfClass:[NSArray class]]) {
            [self cachePlanList:list code:code];
        }
        
        if (cb) {
            cb(nil);
        }
    } onError:^(NSError * _Nonnull error) {
        if (cb) {
            cb(error);
        }
    }];
}

- (void)requestGfcPolicy:(void (^)(NSError *error))cb {
    // 全局频控 APP 生命周期内请求一次即可
    if (self.hasRereshGfcPolicy) {
        if (cb) {
            cb(nil);
        }
        return;
    }
    
    @weakify(self);
    [[IMYPopPlanAPI sharedInstance] getGfcPolicyOnSuccess:^(NSDictionary * _Nonnull dict) {
        @strongify(self);
        [self cacheGfcPolicy:dict];
        self.hasRereshGfcPolicy = YES;
        
        if (cb) {
            cb(nil);
        }
    } onError:^(NSError * _Nonnull error) {
        if (cb) {
            cb(error);
        }
    }];
}

// MARK: - data: cached planList

- (void)cachePlanList:(NSArray *)list code:(NSString *)code {
    NSString *key = [self planListKVKey:code];
    if (list.count > 0) {
        [[IMYKV defaultKV] setArray:list forKey:key];
    } else {
        [[IMYKV defaultKV] removeForKey:key];
    }
}

- (NSArray *)getCachedPlanListWithCode:(NSString *)code {
    NSArray *planList = @[];
    
    NSString *key = [self planListKVKey:code];
    planList = [[IMYKV defaultKV] arrayForKey:key];
    
    return planList;
}

- (NSString *)planListKVKey:(NSString *)code {
    return [NSString stringWithFormat:@"IMYPopPlanManager-888-code-%@", code];
}

// MARK: - data: cached gfc policy

- (void)cacheGfcPolicy:(NSDictionary *)dict {
    NSString *key = [self gfcPolicyKVKey];
    [[IMYKV defaultKV] setDictionary:dict forKey:key];
}

- (NSDictionary *)getCachedGfcPolicy {
    NSDictionary *dict = nil;
    
    NSString *key = [self gfcPolicyKVKey];
    dict = [[IMYKV defaultKV] dictionaryForKey:key];
    
    return dict;
}

- (NSString *)gfcPolicyKVKey {
    return @"IMYPopPlanManager-888-GFCPolicy";
}

// MARK: - data: cached showInfo

- (void)cacheShowInfoWithTime:(NSString *)time planId:(NSString *)planId surveyId:(NSString *)surveyId {
    NSDictionary *lastShowInfo = [self getShowInfoWithSurveyId:surveyId];
    NSMutableDictionary *showInfo = [NSMutableDictionary dictionary];
    
    if (lastShowInfo && imy_isNotEmptyString(lastShowInfo[@"createTime"])) {
        showInfo = [lastShowInfo mutableCopy];
        [showInfo imy_setNonNilObject:time forKey:@"updateTime"];
        [showInfo imy_setNonNilObject:time forKey:@"updateTime4showRule"]; //< 独立，需要根据 repeat_frequency 清空
        [showInfo imy_setNonNilObject:planId forKey:@"planId"];
        [showInfo imy_setNonNilObject:surveyId forKey:@"surveyId"];
    } else {
        [showInfo imy_setNonNilObject:time forKey:@"createTime"];
        [showInfo imy_setNonNilObject:time forKey:@"updateTime"];
        [showInfo imy_setNonNilObject:time forKey:@"updateTime4showRule"]; //< 独立，需要根据 repeat_frequency 清空
        [showInfo imy_setNonNilObject:planId forKey:@"planId"];
        [showInfo imy_setNonNilObject:surveyId forKey:@"surveyId"];
    }
    
    NSInteger showTimes = [showInfo[@"showTimes"] integerValue];
    showTimes = showTimes + 1;
    [showInfo imy_setNonNilObject:@(showTimes) forKey:@"showTimes"];
    
    NSInteger showTimes4showRule = [showInfo[@"showTimes4showRule"] integerValue];
    showTimes4showRule = showTimes4showRule + 1;
    [showInfo imy_setNonNilObject:@(showTimes4showRule) forKey:@"showTimes4showRule"]; //< 独立，需要根据 repeat_frequency 清空
    
    NSString *kvKey = [NSString stringWithFormat:@"IMYPopPlanManager-888-showInfo-%@", surveyId];
    [[IMYKV defaultKV] setDictionary:showInfo forKey:kvKey];
}

- (void)cacheShowInfoWithClose:(BOOL)close surveyId:(NSString *)surveyId {
    NSDictionary *lastShowInfo = [self getShowInfoWithSurveyId:surveyId];
    
    NSMutableDictionary *showInfo = [NSMutableDictionary dictionary];
    if (lastShowInfo) {
        showInfo = [lastShowInfo mutableCopy];
    }
    [showInfo imy_setNonNilObject:surveyId forKey:@"surveyId"];
    [showInfo imy_setNonNilObject:@(YES) forKey:@"close"];
    
    NSString *kvKey = [self showInfoKVKey:surveyId];
    [[IMYKV defaultKV] setDictionary:showInfo forKey:kvKey];
}

/// 重置 show_rule（根据 repeat_frequency 重置 show_rule 的曝光信息）
- (BOOL)repeatShowRuleIfNeedWithSurveyId:(NSString *)surveyId planModel:(IMYPopPlanModel *)planModel {
    NSDictionary *lastShowInfo = [self getShowInfoWithSurveyId:surveyId];
    
    if (!lastShowInfo) {
        return NO;
    }
    
    // show_rule
    NSString *repeat_frequency = planModel.show_rule.repeat_frequency;
    
    // showInfo
    NSString *updateTime4showRule = lastShowInfo[@"updateTime4showRule"];
    NSInteger showTimes4showRule = [lastShowInfo[@"showTimes4showRule"] integerValue];
    
    // isNeedRepeatShowRule
    BOOL isNeedRepeatShowRule = NO;
    BOOL hasOldData = imy_isNotEmptyString(updateTime4showRule) || showTimes4showRule != 0;
    if (hasOldData && [repeat_frequency isEqualToString:@"weekly"]) {
        if (imy_isNotEmptyString(updateTime4showRule)) {
            BOOL isWeekly = [[updateTime4showRule imy_getDateTime] isSameWeekAsDate:[NSDate date]];
            if (!isWeekly) {
                isNeedRepeatShowRule = YES;
            }
        }
    } else if (hasOldData && [repeat_frequency isEqualToString:@"monthly"]) {
        if (imy_isNotEmptyString(updateTime4showRule)) {
            BOOL isMonthly = [[updateTime4showRule imy_getDateTime] isSameMonthAsDate:[NSDate date]];
            if (!isMonthly) {
                isNeedRepeatShowRule = YES;
            }
        }
    }
    
    // 重置
    if (isNeedRepeatShowRule) {
        NSMutableDictionary *showInfo = [lastShowInfo mutableCopy];
        
        showInfo[@"updateTime4showRule"] = @"";
        showInfo[@"showTimes4showRule"] = @(0);
        
        NSString *kvKey = [self showInfoKVKey:surveyId];
        [[IMYKV defaultKV] setDictionary:showInfo forKey:kvKey];
    }
    
    return isNeedRepeatShowRule;
}

- (NSDictionary *)getShowInfoWithSurveyId:(NSString *)surveyId {
    NSString *kvKey = [self showInfoKVKey:surveyId];
    NSDictionary *showInfo = [[IMYKV defaultKV] dictionaryForKey:kvKey];
    return showInfo;
}

- (NSString *)showInfoKVKey:(NSString *)surveyId {
    NSString *kvKey = [NSString stringWithFormat:@"IMYPopPlanManager-888-showInfo-%@", surveyId];
    return kvKey;
}

// MARK: - data: cached lastShowTime

- (void)cacheLastExpInfo:(NSString *)time planId:(NSString *)planId surveyId:(NSString *)surveyId {
    NSString *kvKey = [self lastExpInfoKVKey];
    
    NSMutableDictionary *newLastExpInfo = [NSMutableDictionary dictionary];
    NSDictionary *lastExpInfo = [self getLastExpInfo];
    if (lastExpInfo && [lastExpInfo isKindOfClass:[NSDictionary class]]) {
        newLastExpInfo  = [lastExpInfo mutableCopy];
    }
    
    [newLastExpInfo imy_setNonNilObject:time forKey:@"time"];
    [newLastExpInfo imy_setNonNilObject:surveyId forKey:@"surveyId"];
    [newLastExpInfo imy_setNonNilObject:planId forKey:@"planId"];
    
    [[IMYKV defaultKV] setDictionary:newLastExpInfo forKey:kvKey];
}

- (NSDictionary *)getLastExpInfo {
    NSString *kvKey = [self lastExpInfoKVKey];
    return [[IMYKV defaultKV] dictionaryForKey:kvKey];
}

- (NSString *)lastExpInfoKVKey {
    NSString *kvKey = @"IMYPopPlanManager-888-lastShowTime";
    return kvKey;
}

// MARK: - debug: log

- (void)ppmLog:(NSString *)log
          code:(NSString *)code
       isCache:(BOOL)isCache {
    if (isCache) {
        NSString *log0 = [NSString stringWithFormat:@"\n[code %@][%@]", code, log];
        [self handleLog:log0];
    }
}

- (void)ppmLog:(NSString *)log
        planId:(NSString *)planId
      surveyId:(NSString *)surveyId
       isCache:(BOOL)isCache {
    if (isCache) {
        NSString *log0 = [NSString stringWithFormat:@"[p %@ s %@][%@]", planId, surveyId, log];
        [self handleLog:log0];
    }
}

- (void)handleLog:(NSString *)log {
    NSLog(@"[ppm]%@", log);
    
#ifdef DEBUG
    NSString *kvKey = @"#+IMYPopPlanManager-logArray";
    NSMutableArray *logArrayM = [NSMutableArray array];
    NSArray *logArray = [[IMYKV defaultKV] arrayForKey:kvKey];
    if (logArray) {
        logArrayM = [logArray mutableCopy];
    }
    
    [logArrayM addObject:log];
    [[IMYKV defaultKV] setArray:logArrayM.copy forKey:kvKey];
#endif
}

// MARK: - debug

- (NSArray *)debugGetALLLog {
    NSString *kvkey = @"#+IMYPopPlanManager-logArray";
    NSArray *logs = [[IMYKV defaultKV] arrayForKey:kvkey];
    return logs;;
}

- (NSArray *)debugGetAllPlanList {
    NSMutableArray *planLists = [NSMutableArray array];
    
    NSString *kvKey = [NSString stringWithFormat:@"IMYPopPlanManager-888-code-"];
    [[IMYKV defaultKV].allKeys enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:kvKey]) {
            NSDictionary *dict = [[IMYKV defaultKV] dictionaryForKey:obj];
            [planLists addObject:dict];
        }
    }];
    
    return planLists;
}

- (NSDictionary *)debugGetAllGfcPolicy {
    return [self getCachedGfcPolicy];
}

- (NSArray *)debugGetAllShowInfos {
    NSMutableArray *showInfos = [NSMutableArray array];
    
    NSString *kvKey = [NSString stringWithFormat:@"IMYPopPlanManager-888-showInfo"];
    [[IMYKV defaultKV].allKeys enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:kvKey]) {
            NSDictionary *showInfo = [[IMYKV defaultKV] dictionaryForKey:obj];
            [showInfos addObject:showInfo];
        }
    }];
    
    return showInfos;
}

- (NSString *)debugGetLastShowTime {
    return [[self getLastExpInfo] imy_jsonString];
}

- (void)removeAllCache {
    [[[IMYKV defaultKV] allKeys] enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:@"IMYPopPlanManager-888"]) {
            [[IMYKV defaultKV] removeForKey:obj];
        }
    }];
    self.hasRereshGfcPolicy = NO;
    
    [[IMYKV defaultKV] removeForKey:@"#+IMYPopPlanManager-logArray"];
}

- (void)removePlanList {
    [[[IMYKV defaultKV] allKeys] enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj hasPrefix:@"IMYPopPlanManager-888-code-"]) {
            [[IMYKV defaultKV] removeForKey:obj];
        }
    }];
}

@end
